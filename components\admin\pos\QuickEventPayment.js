import { useState } from 'react'
import PaymentMethodSelector from './PaymentMethodSelector'
import POSSquarePaymentNew from './POSSquarePaymentNew'
import POSSquareTerminal from './POSSquareTerminal'
import POSSquareReader from './POSSquareReader'
import { supabase } from '@/lib/supabase'
import { safeRender } from '@/lib/safe-render-utils'
import styles from '@/styles/admin/POS.module.css'

/**
 * QuickEventPayment component for streamlined payment processing
 *
 * @param {Object} props - Component props
 * @param {Object} props.service - Selected service
 * @param {Object} props.tier - Selected pricing tier
 * @param {Function} props.onBack - Callback to go back
 * @param {Function} props.onComplete - Callback when payment is complete
 * @returns {JSX.Element}
 */
export default function QuickEventPayment({ service, tier, onBack, onComplete }) {
  const [paymentStep, setPaymentStep] = useState('method') // 'method', 'processing'
  const [paymentMethod, setPaymentMethod] = useState(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState(null)

  const totalAmount = parseFloat(tier.price || 0)

  const handlePaymentMethodSelect = (method, methodData) => {
    setPaymentMethod(method)
    setPaymentStep('processing')
    
    if (method === 'cash') {
      // For cash payments, process immediately
      processCashPayment()
    }
    // For card and terminal payments, the respective Square components will handle the flow
  }

  const processCashPayment = async () => {
    try {
      setIsProcessing(true)
      setError(null)

      // Create quick event transaction record
      const result = await createQuickEventTransaction('cash')

      if (result.success) {
        onComplete(result)
      } else {
        throw new Error(result.error || 'Failed to process cash payment')
      }
    } catch (err) {
      console.error('Cash payment error:', err)
      setError(err.message)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleSquarePaymentSuccess = async (paymentResult) => {
    try {
      setIsProcessing(true)
      setError(null)

      // Create quick event transaction record with Square transaction details
      const paymentMethodType = paymentResult.paymentDetails?.deviceId ? 'square_terminal' : 'card'
      const result = await createQuickEventTransaction(paymentMethodType, paymentResult)

      if (result.success) {
        onComplete(result)
      } else {
        throw new Error(result.error || 'Failed to record payment')
      }
    } catch (err) {
      console.error('Payment completion error:', err)
      setError(err.message)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleSquarePaymentError = (error) => {
    console.error('Square payment error:', error)
    setError(error.message || 'Card payment failed')
    setPaymentStep('method')
    setPaymentMethod(null)
  }

  const createQuickEventTransaction = async (paymentMethodType, paymentDetails = null) => {
    try {
      console.log('💳 Creating quick event transaction...', {
        service: service.name,
        tier: tier.name,
        amount: totalAmount,
        paymentMethod: paymentMethodType
      })

      // Get admin session for authentication
      const { data: session, error: sessionError } = await supabase.auth.getSession()
      
      if (sessionError || !session?.session) {
        throw new Error('Authentication required for payment processing')
      }

      console.log('✅ Quick event authentication successful for user:', session.session.user?.email)

      const response = await fetch('/api/admin/pos/create-quick-event', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.session.access_token}`,
        },
        body: JSON.stringify({
          service: {
            id: service.id,
            name: service.name
          },
          tier: {
            id: tier.id,
            name: tier.name,
            duration: tier.duration,
            price: tier.price
          },
          payment: {
            method: paymentMethodType,
            amount: totalAmount,
            currency: 'AUD',
            details: paymentDetails
          }
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      console.log('✅ Quick event transaction created successfully:', result)

      return {
        success: true,
        transaction: result,
        message: 'Quick event payment processed successfully'
      }
    } catch (error) {
      console.error('❌ Error creating quick event transaction:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  const handleBackToMethod = () => {
    setPaymentStep('method')
    setPaymentMethod(null)
    setError(null)
  }

  return (
    <div className={styles.quickEventPayment}>
      <div className={styles.quickPaymentHeader}>
        <h2 className={styles.quickPaymentTitle}>Quick Event Payment</h2>
        <div className={styles.quickPaymentSummary}>
          <div className={styles.serviceSummary}>
            <span className={styles.serviceName}>{safeRender(service.name, 'Service')}</span>
            <span className={styles.tierName}>{safeRender(tier.name, 'Tier')}</span>
            <span className={styles.tierDuration}>({tier.duration} min)</span>
          </div>
          <div className={styles.totalAmount}>
            <span className={styles.amountLabel}>Total:</span>
            <span className={styles.amount}>${totalAmount.toFixed(2)}</span>
          </div>
        </div>
      </div>

      {error && (
        <div className={styles.errorMessage}>
          <span className={styles.errorIcon}>⚠️</span>
          <span>{error}</span>
          <button 
            className={styles.errorRetry}
            onClick={() => setError(null)}
          >
            Dismiss
          </button>
        </div>
      )}

      {paymentStep === 'method' && (
        <PaymentMethodSelector
          onPaymentMethodSelect={handlePaymentMethodSelect}
          amount={totalAmount}
          isLoading={isProcessing}
        />
      )}

      {paymentStep === 'processing' && paymentMethod === 'card' && (
        <POSSquarePaymentNew
          amount={totalAmount}
          currency="AUD"
          onSuccess={handleSquarePaymentSuccess}
          onError={handleSquarePaymentError}
          orderDetails={{
            service: service.name,
            tier: tier.name,
            customer: 'Quick Event Customer'
          }}
        />
      )}

      {paymentStep === 'processing' && paymentMethod === 'square_terminal' && (
        <POSSquareTerminal
          amount={totalAmount}
          currency="AUD"
          onSuccess={handleSquarePaymentSuccess}
          onError={handleSquarePaymentError}
          onCancel={handleBackToMethod}
          orderDetails={{
            service: service.name,
            tier: tier.name,
            customer: 'Quick Event Customer',
            orderId: `quick_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`
          }}
        />
      )}

      {paymentStep === 'processing' && paymentMethod === 'square_reader' && (
        <POSSquareReader
          amount={totalAmount}
          currency="AUD"
          onSuccess={handleSquarePaymentSuccess}
          onError={handleSquarePaymentError}
          onCancel={handleBackToMethod}
          orderDetails={{
            service: service.name,
            tier: tier.name,
            customer: 'Quick Event Customer'
          }}
        />
      )}

      <div className={styles.quickPaymentActions}>
        <button
          className={styles.backButton}
          onClick={onBack}
          disabled={isProcessing}
        >
          ← Back to Services
        </button>
      </div>
    </div>
  )
}
