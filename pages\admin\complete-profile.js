import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useAuth } from '@/contexts/AuthContext';
import Layout from '@/components/Layout';
import ProfileManagementCard from '@/components/admin/ProfileManagementCard';
import { toast } from 'react-toastify';
import styles from '@/styles/CompleteProfile.module.css'; // We might need to create this CSS module

const CompleteProfilePage = () => {
  const { user, loading: authLoading, supabaseClient } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false); // For profile submission loading state
  const [error, setError] = useState('');

  // Check if profile is already complete (simulated check, ideally this would be a flag)
  // This check is more of a safeguard, the primary redirection should happen from activate-account.js
  useEffect(() => {
    if (!authLoading && user && user.id && supabaseClient) {
      const checkProfileStatus = async () => {
        const { data: profile, error: profileError } = await supabaseClient
          .from('artist_profiles')
          .select('artist_name') // Check for a key field indicating completeness
          .eq('user_id', user.id)
          .maybeSingle();

        if (profileError) {
          console.error("Error checking profile status:", profileError);
          // Allow to proceed if error, as they might need to create it
        } else if (profile && profile.artist_name) {
          // Profile seems complete, redirect to dashboard
          console.log('[CompleteProfilePage] Profile already seems complete, redirecting to dashboard.');
          router.push('/admin/artist-braider-dashboard');
        }
      };
      checkProfileStatus();
    }
  }, [user, authLoading, supabaseClient, router]);

  // Protect route: redirect if not logged in or not an artist/braider
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/staff-login?redirect=/admin/complete-profile');
    } else if (user && !['artist', 'braider'].includes(user.role)) {
      console.warn(`[CompleteProfilePage] User with role ${user.role} attempted to access complete-profile page. Redirecting.`);
      toast.error("You don't have permission to access this page.");
      router.push('/staff-login'); // Or a generic dashboard if applicable
    }
  }, [user, authLoading, router]);

  const handleProfileUpdateSuccess = async (updatedProfile) => {
    setIsLoading(true);
    setError('');
    console.log('[CompleteProfilePage] Profile updated successfully:', updatedProfile);
    toast.success('Profile completed! Redirecting to your dashboard...');

    // Now, we need to ensure the `artist_profiles` table reflects completion.
    // The ProfileManagementCard's internal save should handle most of it.
    // We will add/ensure an `is_profile_complete: true` or similar is set by the API.
    // For now, we assume the API /api/artist/profile will handle setting the profile as complete.

    // A more robust way would be to update a specific flag like `is_profile_complete` to true
    // in the `artist_profiles` table via the `/api/artist/profile` endpoint.
    // This is noted as a DB schema consideration.

    // For now, we assume the profile is complete enough if `artist_name` is set,
    // which is handled by ProfileManagementCard.

    // After a short delay to allow toast to be seen.
    setTimeout(() => {
      router.push('/admin/artist-braider-dashboard');
    }, 2000);
  };

  const handleProfileUpdateError = (errorMessage) => {
    setIsLoading(false);
    setError(errorMessage || 'Failed to update profile. Please try again.');
    toast.error(errorMessage || 'Failed to update profile. Please try again.');
  };

  if (authLoading || (!user && !authLoading)) {
    return (
      <Layout>
        <div className={styles.loadingContainer}>
          <p>Loading user data...</p>
        </div>
      </Layout>
    );
  }

  if (!user || !['artist', 'braider'].includes(user.role)) {
     // This case should ideally be caught by the useEffect redirect,
     // but as a fallback, don't render the main content.
    return (
      <Layout>
        <div className={styles.loadingContainer}>
          <p>Redirecting...</p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <Head>
        <title>Complete Your Profile | Staff Portal</title>
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      <div className={styles.container}>
        <h1 className={styles.title}>Welcome! Let's Complete Your Profile</h1>
        <p className={styles.instructions}>
          Please fill out the information below to complete your artist/braider profile.
          This will help us showcase your talents and manage your bookings effectively.
        </p>

        {error && <p className={styles.error}>{error}</p>}

        {user && user.id && (
          <ProfileManagementCard
            artistId={user.id} // Pass the user_id as artistId
            onProfileUpdate={handleProfileUpdateSuccess}
            onProfileError={handleProfileUpdateError} // Add an error handler prop if ProfileManagementCard supports it
            // onSaveSuccessRedirectTo="/admin/artist-braider-dashboard" // Alternative redirect mechanism
          />
        )}

        {/* The submit button is part of ProfileManagementCard, so no separate button here unless ProfileManagementCard is refactored */}
      </div>
    </Layout>
  );
};

export default CompleteProfilePage;

// Basic CSS Module (styles/CompleteProfile.module.css) - Create this file
/*
.container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.title {
  color: #333;
  text-align: center;
  margin-bottom: 1rem;
}

.instructions {
  text-align: center;
  margin-bottom: 2rem;
  color: #555;
}

.error {
  color: red;
  background-color: #ffebee;
  border: 1px solid #ef9a9a;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
  text-align: center;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  font-size: 1.2rem;
}
*/
