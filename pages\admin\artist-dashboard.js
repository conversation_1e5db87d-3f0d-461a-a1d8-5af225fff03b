import { useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import { useAuth } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import ArtistBraiderDashboard from '@/components/admin/ArtistBraiderDashboard'
import styles from '@/styles/admin/ArtistDashboard.module.css'

export default function ArtistDashboardPage() {
  const router = useRouter()
  const { user, role, loading } = useAuth()

  // Redirect to the main artist-braider-dashboard page
  useEffect(() => {
    if (!loading) {
      // Redirect all users to the main artist-braider-dashboard
      router.replace('/admin/artist-braider-dashboard')
    }
  }, [loading, router])

  // Show loading while redirecting
  return (
    <div className={styles.loading}>
      <div className={styles.spinner}></div>
      <p>Redirecting to dashboard...</p>
    </div>
  )
}
