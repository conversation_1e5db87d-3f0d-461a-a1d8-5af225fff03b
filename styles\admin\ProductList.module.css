/* ProductList.module.css */
.productListContainer {
  background-color: #fff;
}

.filters {
  margin-bottom: 1.5rem;
}

.searchBox {
  margin-bottom: 1rem;
}

.searchInput {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.filterControls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.filterItem {
  display: flex;
  flex-direction: column;
  min-width: 150px;
}

.filterItem label {
  font-size: 0.8rem;
  margin-bottom: 4px;
  color: #666;
}

.filterItem select,
.filterItem input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
}

.loadingSpinner {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.errorMessage {
  background-color: #ffebee;
  color: #c62828;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.errorMessage button {
  background-color: #c62828;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  margin-left: 1rem;
  cursor: pointer;
}

.noResults {
  text-align: center;
  padding: 2rem;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.tableContainer {
  overflow-x: auto;
}

.productTable {
  width: 100%;
  border-collapse: collapse;
  text-align: left;
}

.productTable th {
  background-color: #f5f5f5;
  color: #333;
  font-weight: 600;
  padding: 12px 16px;
  cursor: pointer;
  white-space: nowrap;
  user-select: none;
}

.productTable th:hover {
  background-color: #eaeaea;
}

.productTable tbody tr {
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s;
}

.productTable tbody tr:hover {
  background-color: #f9f9f9;
}

.productTable td {
  padding: 12px 16px;
  vertical-align: middle;
}

.imageCell {
  width: 70px;
  text-align: center;
}

.noImage {
  width: 50px;
  height: 50px;
  border: 1px dashed #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 0.7rem;
  margin: 0 auto;
}

.imageFallback {
  width: 50px;
  height: 50px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 1.2rem;
  margin: 0 auto;
}

.sortIndicator {
  display: inline-block;
  margin-left: 5px;
}

.stockBadge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
  min-width: 40px;
}

.inStock {
  background-color: rgba(46, 125, 50, 0.1);
  color: #2e7d32;
}

.lowStock {
  background-color: rgba(245, 124, 0, 0.1);
  color: #f57c00;
}

.outOfStock {
  background-color: rgba(198, 40, 40, 0.1);
  color: #c62828;
}

.statusBadge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.statusActive {
  background-color: rgba(46, 125, 50, 0.1);
  color: #2e7d32;
}

.statusInactive {
  background-color: rgba(198, 40, 40, 0.1);
  color: #c62828;
}

.actions {
  white-space: nowrap;
  display: flex;
  gap: 8px;
}

.editButton {
  display: inline-block;
  background-color: #6a0dad;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  border: none;
}

.editButton:hover {
  background-color: #5a0c8f;
}

.stockButton {
  display: inline-block;
  background-color: #4a90e2;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  border: none;
}

.stockButton:hover {
  background-color: #3a7bc8;
}

.deleteButton {
  display: inline-block;
  background-color: #dc3545;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  border: none;
}

.deleteButton:hover {
  background-color: #c82333;
}

/* Delete confirmation modal */
.deleteModal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.deleteModalContent {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.deleteModalContent h3 {
  margin: 0 0 1rem 0;
  color: #dc3545;
}

.deleteModalContent p {
  margin: 0 0 1.5rem 0;
  color: #666;
  line-height: 1.5;
}

.deleteModalActions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.cancelButton {
  background-color: #6c757d;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
}

.cancelButton:hover {
  background-color: #5a6268;
}

.confirmDeleteButton {
  background-color: #dc3545;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
}

.confirmDeleteButton:hover {
  background-color: #c82333;
}

@media (max-width: 768px) {
  .filterControls {
    flex-direction: column;
    gap: 0.5rem;
  }

  .filterItem {
    min-width: auto;
  }

  .productTable th,
  .productTable td {
    padding: 8px;
  }
}
