import { supabase } from '@/lib/supabase';
import { safeSerializeData } from '@/lib/safe-render-utils';
import { setCacheHeaders } from '@/lib/cache-control-utils';

/**
 * Public API endpoint for fetching services
 * This endpoint provides services data for the public website
 * No authentication required - only returns active services
 */
export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Fetch active services from database (using services table instead of view for public access)
    // Apply visibility filter for public book-online page
    // Filter for longer-duration services (2-6 hours = 120-360 minutes) as per Ocean Soul Sparkles requirements
    const { data: services, error } = await supabase
      .from('services')
      .select('*')
      .eq('status', 'active')
      .eq('visible_on_public', true)
      .gte('duration', 120)
      .lte('duration', 360)
      .order('name');

    if (error) {
      console.error('Error fetching services:', error);
      return res.status(500).json({ error: 'Failed to fetch services' });
    }

    // Transform database services to match the format expected by the frontend
    const transformedServices = services.map(service => ({
      id: String(service.id || ''),
      title: String(service.name || ''),
      description: String(service.description || ''),
      image: String(service.image_url || '/images/services/face-paint.jpg'),
      category: String(service.category || 'general'),
      icon: getCategoryIcon(service.category),
      pricing: formatPricing(service.price, service.duration),
      accentColor: String(service.color || '#4ECDC4'),
      duration: Number(service.duration) || 0,
      featured: Boolean(service.featured),
      pricingTiers: [] // No pricing tiers from regular services table
    }));

    // Serialize the data to ensure no complex objects are passed to frontend
    const serializedServices = safeSerializeData(transformedServices);

    // Set appropriate cache headers for services data (public endpoint, static data)
    setCacheHeaders(res, 'services_with_pricing', 'GET', false, req.query);

    return res.status(200).json({ services: serializedServices });

  } catch (error) {
    console.error('Error in services API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Get category icon based on service category
 */
function getCategoryIcon(category) {
  const iconMap = {
    'painting': '🎨',
    'airbrush': '🎨',
    'braiding': '💇',
    'hair': '💇',
    'glitter': '✨',
    'sparkle': '✨',
    'special': '🎭',
    'uv': '🌟'
  };

  return iconMap[category] || '🎨';
}



/**
 * Format pricing information based on service price and duration
 */
function formatPricing(price, duration) {
  const basePrice = parseFloat(price) || 0;

  // Generate pricing tiers based on the base price
  return [
    {
      title: 'Individual service',
      price: `$${basePrice.toFixed(0)}`
    },
    {
      title: 'Group bookings (5+ people)',
      price: `from $${(basePrice * 0.85).toFixed(0)} per person`
    },
    {
      title: 'Event packages',
      price: 'Please contact for custom quotes'
    },
    {
      title: `Duration: ${duration} minutes`,
      price: ''
    }
  ];
}
