import { useState, useEffect, useCallback, useMemo } from 'react';
import { getAuthToken } from '@/lib/auth-utils';
import { resolveImageUrl } from '@/lib/image-utils';
import PricingTiersForm from './PricingTiersForm';
import styles from '@/styles/admin/ServiceForm.module.css';

export default function ServiceForm({ service, onSave, onCancel, onDelete }) {
  console.log('🔍 ServiceForm - Component function executing', { service });

  // Form state - ensure all values are primitive types to prevent React Error #130
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    duration: '',
    price: '',
    color: '#6a0dad',
    category: '',
    category_id: '',
    image_url: '',
    status: 'active',
    featured: false,
    visible_on_public: true,
    visible_on_pos: true,
    visible_on_events: true
  });

  // Component state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [pricingTiers, setPricingTiers] = useState([]);
  const [serviceCategories, setServiceCategories] = useState([]);

  // Stable callback for pricing tiers to prevent infinite loops
  const handlePricingTiersChange = useCallback((newTiers) => {
    setPricingTiers(newTiers);
  }, []);

  // Memoize pricing tiers to prevent infinite loops
  const initialPricingTiers = useMemo(() => {
    console.log('🔍 ServiceForm - initialPricingTiers useMemo called', {
      service: service?.id,
      hasPricingTiers: service?.pricing_tiers ? true : false,
      pricingTiersLength: service?.pricing_tiers?.length,
      pricingTiers: service?.pricing_tiers
    });

    if (!service) {
      console.log('🔍 ServiceForm - No service, returning empty array');
      return [];
    }

    if (service.pricing_tiers && Array.isArray(service.pricing_tiers)) {
      console.log('🔍 ServiceForm - Service has pricing tiers:', service.pricing_tiers.length);
      return service.pricing_tiers;
    } else {
      console.log('🔍 ServiceForm - No pricing tiers found, creating default tier');
      return [{
        id: null,
        name: 'Standard',
        description: 'Standard service duration and pricing',
        duration: String(service.duration || '60'),
        price: String(service.price || '50'),
        is_default: true,
        sort_order: 1
      }];
    }
  }, [service?.id, service?.pricing_tiers]); // Only depend on service ID and pricing_tiers

  // Load service categories - use hardcoded categories that match database
  useEffect(() => {
    // Use the same categories as ServiceList and POS Terminal for consistency
    const categories = [
      'Hair & Braiding',
      'Glitter & Gems',
      'Body Painting',
      'Face Painting',
      'Airbrush',
      'Special'
    ];
    setServiceCategories(categories);
  }, []);

  // Load service data if editing
  useEffect(() => {
    console.log('🔍 ServiceForm - useEffect called', {
      serviceId: service?.id,
      initialPricingTiersLength: initialPricingTiers?.length,
      initialPricingTiers
    });

    if (service) {
      console.log('🔍 ServiceForm - Setting form data for service:', service.name);

      setFormData({
        name: String(service.name || ''),
        description: String(service.description || ''),
        duration: String(service.duration || ''),
        price: String(service.price || ''),
        color: String(service.color || '#6a0dad'),
        category: String(service.category || ''),
        category_id: String(service.category_id || ''),
        image_url: String(resolveImageUrl(service.image_url) || ''),
        status: String(service.status || 'active'),
        featured: Boolean(service.featured),
        visible_on_public: Boolean(service.visible_on_public),
        visible_on_pos: Boolean(service.visible_on_pos),
        visible_on_events: Boolean(service.visible_on_events)
      });

      // Set pricing tiers from memoized value
      console.log('🔍 ServiceForm - Setting pricing tiers:', initialPricingTiers);
      setPricingTiers(initialPricingTiers);
    }
  }, [service?.id, initialPricingTiers]); // Only depend on service ID and memoized pricing tiers

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : String(value)
    }));

    // Clear validation error when user starts typing
    if (validationErrors[name]) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  // Validate form data
  const validateForm = () => {
    const errors = {};

    // Required fields
    if (!formData.name.trim()) {
      errors.name = 'Service name is required';
    }

    if (!formData.price.trim()) {
      errors.price = 'Price is required';
    } else {
      const priceNum = parseFloat(formData.price);
      if (isNaN(priceNum) || priceNum < 0) {
        errors.price = 'Price must be a valid positive number';
      }
    }

    if (!formData.duration.trim()) {
      errors.duration = 'Duration is required';
    } else {
      const durationNum = parseInt(formData.duration, 10);
      if (isNaN(durationNum) || durationNum <= 0) {
        errors.duration = 'Duration must be a valid positive number (in minutes)';
      }
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle image upload
  const handleImageUpload = async (file) => {
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      setError('Invalid file type. Please upload JPEG, PNG, or WebP images only.');
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      setError('File size too large. Please upload images smaller than 10MB.');
      return;
    }

    setUploadingImage(true);
    setError(null);

    try {
      const formDataUpload = new FormData();
      formDataUpload.append('image', file);

      // Get the current auth token
      const token = await getAuthToken();

      const response = await fetch('/api/admin/uploads/image', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token || ''}`,
        },
        body: formDataUpload
      });

      console.log('🔍 ServiceForm - Upload response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('🔍 ServiceForm - Upload error response:', errorText);
        throw new Error(`Failed to upload image: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('🔍 ServiceForm - Image upload result:', data);

      if (data.success && data.url) {
        console.log('🔍 ServiceForm - Setting image URL to:', data.url);
        setFormData(prev => ({
          ...prev,
          image_url: String(data.url || '')
        }));
        console.log('✅ ServiceForm - Image uploaded successfully:', data.url);
      } else {
        throw new Error(data.error || 'Upload failed - no URL returned');
      }
    } catch (err) {
      console.error('Error uploading image:', err);
      setError('Failed to upload image. Please try again.');
    } finally {
      setUploadingImage(false);
    }
  };

  // Handle file input change
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      handleImageUpload(file);
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      setError('Please fix the validation errors before submitting.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Validate pricing tiers
      if (!pricingTiers || pricingTiers.length === 0) {
        setError('At least one pricing tier is required.');
        return;
      }

      // Validate each pricing tier
      for (let i = 0; i < pricingTiers.length; i++) {
        const tier = pricingTiers[i];
        if (!tier.name || !tier.duration || !tier.price) {
          setError(`Pricing tier ${i + 1} is incomplete. Please fill in all required fields.`);
          return;
        }
      }

      // Prepare data for API - ensure all values are properly typed
      const serviceData = {
        name: String(formData.name).trim(),
        description: String(formData.description).trim(),
        duration: parseInt(formData.duration, 10),
        price: parseFloat(formData.price),
        color: String(formData.color),
        category: String(formData.category).trim(),
        category_id: formData.category_id || null,
        image_url: String(formData.image_url).trim(),
        status: String(formData.status),
        featured: Boolean(formData.featured),
        visible_on_public: Boolean(formData.visible_on_public),
        visible_on_pos: Boolean(formData.visible_on_pos),
        visible_on_events: Boolean(formData.visible_on_events),
        pricingTiers: pricingTiers.map(tier => ({
          id: tier.id,
          name: String(tier.name).trim(),
          description: String(tier.description).trim(),
          duration: parseInt(tier.duration, 10),
          price: parseFloat(tier.price),
          is_default: Boolean(tier.is_default),
          sort_order: Number(tier.sort_order)
        }))
      };

      console.log('🔍 ServiceForm - Submitting service data', serviceData);

      // Create or update service
      const url = service ? `/api/admin/services/${service.id}` : '/api/admin/services';
      const method = service ? 'PUT' : 'POST';

      // Get the current auth token
      const token = await getAuthToken();

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token || ''}`
        },
        body: JSON.stringify(serviceData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save service');
      }

      const { service: savedService } = await response.json();

      console.log('🔍 ServiceForm - Service saved successfully', savedService);

      // Call onSave callback
      if (onSave) {
        onSave(savedService);
      }
    } catch (err) {
      console.error('Error saving service:', err);
      setError(err.message || 'Failed to save service');
    } finally {
      setLoading(false);
    }
  };

  // Handle service deletion
  const handleDelete = async () => {
    if (!service || !service.id) return;

    try {
      setLoading(true);
      setError(null);

      // Get the current auth token
      const token = await getAuthToken();

      const response = await fetch(`/api/admin/services/${service.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token || ''}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete service');
      }

      console.log('🔍 ServiceForm - Service deleted successfully');
      setShowDeleteConfirm(false);

      // Call onDelete callback if provided
      if (onDelete) {
        onDelete(service);
      }
    } catch (err) {
      console.error('Error deleting service:', err);
      setError(err.message || 'Failed to delete service');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.serviceForm}>
      <div className={styles.formHeader}>
        <h2>{service ? 'Edit Service' : 'Add New Service'}</h2>
      </div>

      <form className={styles.form} onSubmit={handleSubmit}>
        {error && (
          <div className={styles.error}>
            {error}
          </div>
        )}

        {/* Basic Information Section */}
        <div className={styles.section}>
          <h3>Basic Information</h3>
          
          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="name">
                Service Name <span className={styles.requiredField}>*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className={`${styles.input} ${validationErrors.name ? styles.fieldError : ''}`}
                disabled={loading}
                placeholder="Enter service name"
              />
              {validationErrors.name && (
                <span className={styles.helpText} style={{ color: '#e74c3c' }}>
                  {validationErrors.name}
                </span>
              )}
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="category">Category</label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleChange}
                className={styles.select}
                disabled={loading}
              >
                <option value="">Select Category</option>
                {serviceCategories.map(category => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              className={styles.textarea}
              disabled={loading}
              placeholder="Describe the service..."
              rows={4}
            />
          </div>
        </div>

        {/* Pricing & Duration Section */}
        <div className={styles.section}>
          <h3>Pricing & Duration</h3>
          
          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="price">
                Price (AUD) <span className={styles.requiredField}>*</span>
              </label>
              <input
                type="number"
                id="price"
                name="price"
                value={formData.price}
                onChange={handleChange}
                className={`${styles.input} ${validationErrors.price ? styles.fieldError : ''}`}
                disabled={loading}
                placeholder="0.00"
                step="0.01"
                min="0"
              />
              {validationErrors.price && (
                <span className={styles.helpText} style={{ color: '#e74c3c' }}>
                  {validationErrors.price}
                </span>
              )}
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="duration">
                Duration (minutes) <span className={styles.requiredField}>*</span>
              </label>
              <input
                type="number"
                id="duration"
                name="duration"
                value={formData.duration}
                onChange={handleChange}
                className={`${styles.input} ${validationErrors.duration ? styles.fieldError : ''}`}
                disabled={loading}
                placeholder="60"
                min="1"
              />
              {validationErrors.duration && (
                <span className={styles.helpText} style={{ color: '#e74c3c' }}>
                  {validationErrors.duration}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Image Section */}
        <div className={styles.section}>
          <h3>Service Image</h3>

          {formData.image_url ? (
            <div className={styles.imagePreviewContainer}>
              <div className={styles.imagePreview}>
                <img
                  src={formData.image_url}
                  alt="Service preview"
                  className={styles.previewImage}
                />
                <div className={styles.imageActions}>
                  <label htmlFor="imageUpload" className={styles.changeImageButton}>
                    {uploadingImage ? (
                      <>
                        <span className={styles.loadingSpinner}></span>
                        Uploading...
                      </>
                    ) : (
                      'Change Image'
                    )}
                  </label>
                  <button
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, image_url: '' }))}
                    className={styles.removeImageButton}
                    disabled={loading || uploadingImage}
                  >
                    Remove Image
                  </button>
                </div>
              </div>
              <div className={styles.imagePath}>
                <strong>Current Image:</strong> {formData.image_url}
              </div>
            </div>
          ) : (
            <div className={styles.noImageContainer}>
              <div className={styles.noImagePlaceholder}>
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                  <circle cx="8.5" cy="8.5" r="1.5"/>
                  <polyline points="21,15 16,10 5,21"/>
                </svg>
                <p>No image selected</p>
              </div>
              <div className={styles.imageUploadOptions}>
                <label htmlFor="imageUpload" className={styles.uploadButton}>
                  {uploadingImage ? (
                    <>
                      <span className={styles.loadingSpinner}></span>
                      Uploading...
                    </>
                  ) : (
                    <>
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                        <polyline points="7,10 12,15 17,10"/>
                        <line x1="12" y1="15" x2="12" y2="3"/>
                      </svg>
                      Upload Image
                    </>
                  )}
                </label>
              </div>
            </div>
          )}

          <input
            type="file"
            id="imageUpload"
            className={styles.fileInput}
            accept="image/jpeg,image/jpg,image/png,image/webp"
            onChange={handleFileChange}
            disabled={loading || uploadingImage}
          />

          <div className={styles.helpText}>
            <p>💡 <strong>Tip:</strong> Upload images directly for best performance and reliability. Supported formats: JPEG, PNG, WebP (max 10MB)</p>
          </div>
        </div>

        {/* Pricing Tiers Section */}
        <div className={styles.section}>
          <PricingTiersForm
            pricingTiers={pricingTiers}
            onChange={handlePricingTiersChange}
          />
        </div>

        {/* Appearance & Settings Section */}
        <div className={styles.section}>
          <h3>Appearance & Settings</h3>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="color">Accent Color</label>
              <input
                type="color"
                id="color"
                name="color"
                value={formData.color}
                onChange={handleChange}
                className={styles.colorInput}
                disabled={loading}
              />
              <span className={styles.helpText}>
                This color will be used for service highlights and theming
              </span>
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="status">Status</label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleChange}
                className={styles.select}
                disabled={loading}
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="draft">Draft</option>
              </select>
            </div>
          </div>

          <div className={styles.formGroup}>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                name="featured"
                checked={formData.featured}
                onChange={handleChange}
                disabled={loading}
              />
              Featured Service
            </label>
            <span className={styles.helpText}>
              Featured services are highlighted on the services page
            </span>
          </div>
        </div>

        {/* Service Visibility Section */}
        <div className={styles.section}>
          <h3>Service Visibility</h3>
          <p className={styles.helpText}>
            Control where this service appears. Uncheck to hide from specific areas.
          </p>

          <div className={styles.visibilityControls}>
            <div className={styles.formGroup}>
              <label className={styles.checkboxLabel}>
                <input
                  type="checkbox"
                  name="visible_on_public"
                  checked={formData.visible_on_public}
                  onChange={handleChange}
                  disabled={loading}
                />
                Public Book-Online Page
              </label>
              <span className={styles.helpText}>
                Show this service on the public booking page for customers
              </span>
            </div>

            <div className={styles.formGroup}>
              <label className={styles.checkboxLabel}>
                <input
                  type="checkbox"
                  name="visible_on_pos"
                  checked={formData.visible_on_pos}
                  onChange={handleChange}
                  disabled={loading}
                />
                POS Terminal
              </label>
              <span className={styles.helpText}>
                Show this service in the Point of Sale terminal interface
              </span>
            </div>

            <div className={styles.formGroup}>
              <label className={styles.checkboxLabel}>
                <input
                  type="checkbox"
                  name="visible_on_events"
                  checked={formData.visible_on_events}
                  onChange={handleChange}
                  disabled={loading}
                />
                Events Booking
              </label>
              <span className={styles.helpText}>
                Show this service for special events and photoshoot bookings
              </span>
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className={styles.formActions}>
          <div className={styles.leftActions}>
            {service && (
              <button
                type="button"
                className={styles.deleteButton}
                onClick={() => setShowDeleteConfirm(true)}
                disabled={loading}
              >
                Delete Service
              </button>
            )}
          </div>
          <div className={styles.rightActions}>
            <button
              type="button"
              onClick={onCancel}
              className={styles.cancelButton}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className={styles.saveButton}
              disabled={loading || uploadingImage}
            >
              {loading ? (
                <>
                  <span className={styles.loadingSpinner}></span>
                  {service ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                service ? 'Update Service' : 'Create Service'
              )}
            </button>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <div className={styles.deleteModal}>
            <div className={styles.deleteModalContent}>
              <h3>Confirm Delete</h3>
              <p>
                Are you sure you want to delete the service "{service?.name}"?
              </p>
              <p className={styles.warningText}>
                <strong>Warning:</strong> This action cannot be undone. The service will be removed from:
              </p>
              <ul className={styles.impactList}>
                <li>Admin service management</li>
                {service?.visible_on_public && <li>Public Book Online page</li>}
                {service?.visible_on_pos && <li>POS Terminal interface</li>}
                {service?.visible_on_events && <li>Events booking system</li>}
              </ul>
              <div className={styles.deleteModalActions}>
                <button
                  type="button"
                  className={styles.cancelButton}
                  onClick={() => setShowDeleteConfirm(false)}
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className={styles.confirmDeleteButton}
                  onClick={handleDelete}
                  disabled={loading}
                >
                  {loading ? 'Deleting...' : 'Delete Service'}
                </button>
              </div>
            </div>
          </div>
        )}
      </form>
    </div>
  );
}
