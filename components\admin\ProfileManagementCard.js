import { useState, useRef } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'react-toastify'
import styles from '@/styles/admin/ProfileManagementCard.module.css'

export default function ProfileManagementCard({ profile, onProfileUpdate }) {
  const { user } = useAuth()
  const [isEditing, setIsEditing] = useState(false)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState({
    artist_name: profile?.artist_name || '',
    display_name: profile?.display_name || '',
    bio: profile?.bio || '',
    specializations: profile?.specializations || [],
    skill_level: profile?.skill_level || 'intermediate',
    hourly_rate: profile?.hourly_rate || '',
    profile_image_url: profile?.profile_image_url || ''
  })
  const fileInputRef = useRef(null)

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSpecializationChange = (specialization) => {
    setFormData(prev => ({
      ...prev,
      specializations: prev.specializations.includes(specialization)
        ? prev.specializations.filter(s => s !== specialization)
        : [...prev.specializations, specialization]
    }))
  }

  const handleImageUpload = async (e) => {
    const file = e.target.files[0]
    if (!file) return

    // Validate file type and size
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file')
      return
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      toast.error('Image size must be less than 5MB')
      return
    }

    try {
      setSaving(true)
      
      // Create FormData for file upload
      const uploadData = new FormData()
      uploadData.append('file', file)
      uploadData.append('type', 'profile_image')

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user.access_token}`
        },
        body: uploadData
      })

      if (!response.ok) {
        throw new Error('Failed to upload image')
      }

      const result = await response.json()
      setFormData(prev => ({
        ...prev,
        profile_image_url: result.url
      }))

      toast.success('Profile image uploaded successfully')
    } catch (error) {
      console.error('Error uploading image:', error)
      toast.error('Failed to upload image')
    } finally {
      setSaving(false)
    }
  }

  const handleSave = async () => {
    try {
      setSaving(true)

      const response = await fetch('/api/artist/profile', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${user.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        throw new Error('Failed to update profile')
      }

      const updatedProfile = await response.json()
      onProfileUpdate(updatedProfile)
      setIsEditing(false)
      toast.success('Profile updated successfully')
    } catch (error) {
      console.error('Error updating profile:', error)
      toast.error('Failed to update profile')
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    setFormData({
      artist_name: profile?.artist_name || '',
      display_name: profile?.display_name || '',
      bio: profile?.bio || '',
      specializations: profile?.specializations || [],
      skill_level: profile?.skill_level || 'intermediate',
      hourly_rate: profile?.hourly_rate || '',
      profile_image_url: profile?.profile_image_url || ''
    })
    setIsEditing(false)
  }

  const availableSpecializations = [
    'braiding', 'hair', 'painting', 'glitter', 'uv', 'makeup', 'temporary_tattoos'
  ]

  const skillLevels = [
    { value: 'beginner', label: 'Beginner' },
    { value: 'intermediate', label: 'Intermediate' },
    { value: 'advanced', label: 'Advanced' },
    { value: 'expert', label: 'Expert' }
  ]

  return (
    <div className={styles.card}>
      <div className={styles.header}>
        <h3>Profile Management</h3>
        {!isEditing && (
          <button 
            onClick={() => setIsEditing(true)}
            className={styles.editButton}
          >
            Edit Profile
          </button>
        )}
      </div>

      <div className={styles.content}>
        {/* Profile Image */}
        <div className={styles.imageSection}>
          <div className={styles.imageContainer}>
            {formData.profile_image_url ? (
              <img 
                src={formData.profile_image_url} 
                alt="Profile" 
                className={styles.profileImage}
              />
            ) : (
              <div className={styles.imagePlaceholder}>
                <span>No Image</span>
              </div>
            )}
          </div>
          
          {isEditing && (
            <div className={styles.imageActions}>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleImageUpload}
                accept="image/*"
                style={{ display: 'none' }}
              />
              <button 
                onClick={() => fileInputRef.current?.click()}
                className={styles.uploadButton}
                disabled={saving}
              >
                {saving ? 'Uploading...' : 'Change Photo'}
              </button>
            </div>
          )}
        </div>

        {/* Profile Form */}
        <div className={styles.formSection}>
          <div className={styles.formGroup}>
            <label>Artist Name</label>
            {isEditing ? (
              <input
                type="text"
                name="artist_name"
                value={formData.artist_name}
                onChange={handleInputChange}
                className={styles.input}
              />
            ) : (
              <p className={styles.value}>{profile?.artist_name || 'Not set'}</p>
            )}
          </div>

          <div className={styles.formGroup}>
            <label>Display Name</label>
            {isEditing ? (
              <input
                type="text"
                name="display_name"
                value={formData.display_name}
                onChange={handleInputChange}
                className={styles.input}
              />
            ) : (
              <p className={styles.value}>{profile?.display_name || 'Not set'}</p>
            )}
          </div>

          <div className={styles.formGroup}>
            <label>Bio</label>
            {isEditing ? (
              <textarea
                name="bio"
                value={formData.bio}
                onChange={handleInputChange}
                className={styles.textarea}
                rows={3}
              />
            ) : (
              <p className={styles.value}>{profile?.bio || 'No bio available'}</p>
            )}
          </div>

          <div className={styles.formGroup}>
            <label>Specializations</label>
            {isEditing ? (
              <div className={styles.specializationGrid}>
                {availableSpecializations.map(spec => (
                  <label key={spec} className={styles.checkboxLabel}>
                    <input
                      type="checkbox"
                      checked={formData.specializations.includes(spec)}
                      onChange={() => handleSpecializationChange(spec)}
                    />
                    <span>{spec.charAt(0).toUpperCase() + spec.slice(1).replace('_', ' ')}</span>
                  </label>
                ))}
              </div>
            ) : (
              <div className={styles.specializationTags}>
                {profile?.specializations?.map(spec => (
                  <span key={spec} className={styles.tag}>
                    {spec.charAt(0).toUpperCase() + spec.slice(1).replace('_', ' ')}
                  </span>
                )) || <span className={styles.noData}>No specializations set</span>}
              </div>
            )}
          </div>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label>Skill Level</label>
              {isEditing ? (
                <select
                  name="skill_level"
                  value={formData.skill_level}
                  onChange={handleInputChange}
                  className={styles.select}
                >
                  {skillLevels.map(level => (
                    <option key={level.value} value={level.value}>
                      {level.label}
                    </option>
                  ))}
                </select>
              ) : (
                <p className={styles.value}>
                  {skillLevels.find(l => l.value === profile?.skill_level)?.label || 'Not set'}
                </p>
              )}
            </div>

            <div className={styles.formGroup}>
              <label>Hourly Rate</label>
              {isEditing ? (
                <input
                  type="number"
                  name="hourly_rate"
                  value={formData.hourly_rate}
                  onChange={handleInputChange}
                  className={styles.input}
                  min="0"
                  step="0.01"
                />
              ) : (
                <p className={styles.value}>
                  {profile?.hourly_rate ? `$${profile.hourly_rate}` : 'Not set'}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        {isEditing && (
          <div className={styles.actions}>
            <button 
              onClick={handleCancel}
              className={styles.cancelButton}
              disabled={saving}
            >
              Cancel
            </button>
            <button 
              onClick={handleSave}
              className={styles.saveButton}
              disabled={saving}
            >
              {saving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
