import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'react-toastify'
import Layout from '@/components/Layout'
import styles from '@/styles/StaffLogin.module.css'

export default function StaffLogin() {
  const router = useRouter()
  const { signIn, user, loading: authLoading } = useAuth()
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  // Check if user is already authenticated and redirect appropriately
  useEffect(() => {
    if (!authLoading && user) {
      // Redirect based on user role or return URL
      const returnUrl = router.query.redirect || '/admin/dashboard'
      router.push(returnUrl)
    }
  }, [user, authLoading, router])

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    // Clear error when user starts typing
    if (error) setError(null)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    const { email, password } = formData

    // Basic validation
    if (!email || !password) {
      setError('Please enter both email and password')
      setLoading(false)
      return
    }

    console.log('[Staff Login] Starting authentication for:', email)

    try {
      // Sign in with email and password
      console.log('[Staff Login] Calling signIn function...')
      const result = await signIn(email, password)

      console.log('[Staff Login] SignIn result:', {
        hasData: !!result.data,
        hasError: !!result.error,
        errorMessage: result.error?.message
      })

      // Comprehensive check for user data and role before checking result.error
      if (!result.data || !result.data.user || !result.data.role) {
        console.error('[Staff Login] Authentication response missing user data or role:', result);
        setError('Authentication failed: User information is incomplete. Please contact support.');
        toast.error('Login failed: Incomplete user data received.');
        setLoading(false);
        return;
      }

      // Now, handle explicit errors from the signIn function (e.g., network issues, Supabase errors)
      if (result.error) {
        console.error('[Staff Login] Authentication error from signIn:', result.error)
        setError(result.error.message || 'Authentication failed. Please check your credentials.')
        toast.error('Login failed. Please check your credentials and try again.')
        setLoading(false)
        return
      }

      // At this point, we have result.data.user and result.data.role
      // Explicitly check the role against allowed staff roles
      const allowedStaffRoles = ['artist', 'braider', 'admin', 'dev'];
      if (!allowedStaffRoles.includes(result.data.role)) {
        console.warn(`[Staff Login] User ${result.data.user.email} authenticated with an unsupported role for staff portal: ${result.data.role}`);
        setError(`Access denied: Your role (${result.data.role}) is not permitted for the staff portal. Please contact support if you believe this is an error.`);
        toast.error('Login failed: Insufficient permissions.');
        setLoading(false);
        // Optionally, sign the user out if they were technically logged in with an invalid role for this portal
        // await signOut(); // If signOut is available in this context, or manage through AuthContext
        return;
      }

      // All checks passed, authentication is successful and role is appropriate
      console.log(`[Staff Login] ✅ Authentication successful for ${result.data.user.email} with role: ${result.data.role}`)

      // Show success message
      toast.success('Welcome back!')
      setLoading(false) // Ensure loading is false on success before redirect

      // Redirect will be handled by useEffect when user state updates
      // No need for an else block here, as prior checks handle all failure cases.

    } catch (err) {
      console.error('[Staff Login] Unexpected error during authentication:', err.message, err.stack)
      setError('An unexpected error occurred. Please try again.')
      toast.error('Login failed. Please try again later.')
      setLoading(false)
    }
  }

  // Show loading while checking auth
  if (authLoading) {
    return (
      <Layout>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading...</p>
        </div>
      </Layout>
    )
  }

  // Don't render form if user is already authenticated (redirect will happen)
  if (user) {
    return null
  }

  return (
    <Layout>
      <Head>
        <title>Staff Login | Ocean Soul Sparkles</title>
        <meta name="description" content="Staff login portal for Ocean Soul Sparkles team members, artists, and administrators." />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <main className={styles.main}>
        <div className={styles.container}>
          <div className={styles.header}>
            <div className={styles.logo}>
              <h1>Ocean Soul Sparkles</h1>
              <div className={styles.subtitle}>Staff Portal</div>
            </div>
            <p className={styles.description}>
              Welcome back! Please sign in to access your staff dashboard.
            </p>
          </div>

          <div className={styles.formWrapper}>
            <form onSubmit={handleSubmit} className={styles.form}>
              <div className={styles.formGroup}>
                <label htmlFor="email" className={styles.label}>
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={styles.input}
                  placeholder="Enter your work email"
                  required
                  autoComplete="email"
                />
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="password" className={styles.label}>
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className={styles.input}
                  placeholder="Enter your password"
                  required
                  autoComplete="current-password"
                />
              </div>

              {error && (
                <div className={styles.error}>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="10"/>
                    <line x1="15" y1="9" x2="9" y2="15"/>
                    <line x1="9" y1="9" x2="15" y2="15"/>
                  </svg>
                  {error}
                </div>
              )}

              <button
                type="submit"
                className={styles.submitButton}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <div className={styles.buttonSpinner}></div>
                    Signing In...
                  </>
                ) : (
                  'Sign In'
                )}
              </button>
            </form>
          </div>

          <div className={styles.footer}>
            <div className={styles.helpText}>
              <p>
                <strong>For Staff Members:</strong> Use the credentials provided by your administrator.
              </p>
              <p>
                <strong>Need Help?</strong> Contact your administrator or email{' '}
                <a href="mailto:<EMAIL>" className={styles.link}>
                  <EMAIL>
                </a>
              </p>
            </div>

            <div className={styles.publicLinks}>
              <Link href="/" className={styles.link}>
                ← Back to Main Website
              </Link>
              <span className={styles.separator}>|</span>
              <Link href="/login" className={styles.link}>
                Customer Login
              </Link>
            </div>
          </div>
        </div>
      </main>
    </Layout>
  )
}
