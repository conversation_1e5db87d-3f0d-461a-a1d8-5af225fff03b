import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import { useAuth } from '@/contexts/AuthContext'
import styles from '@/styles/admin/Login.module.css'

export default function ActivateAccount() {
  const router = useRouter()
  const { token } = router.query
  const { signIn } = useAuth()

  const [step, setStep] = useState('validating') // validating, form, success, error
  const [tokenValidation, setTokenValidation] = useState({ valid: false, error: null })
  const [userInfo, setUserInfo] = useState(null)
  const [activationResult, setActivationResult] = useState(null)
  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: ''
  })
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState('')
  const [passwordValidation, setPasswordValidation] = useState({
    hasLowercase: false,
    hasUppercase: false,
    hasNumber: false,
    hasSpecialChar: false,
    hasMinLength: false,
    passwordsMatch: false
  })

  // Validate token on component mount
  useEffect(() => {
    if (token) {
      validateActivationToken(token)
    }
  }, [token])

  const validateActivationToken = async (activationToken) => {
    try {
      console.log('Validating activation token:', activationToken.substring(0, 8) + '...')
      
      const response = await fetch('/api/auth/validate-activation-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: activationToken }),
      })

      const result = await response.json()

      if (response.ok && result.valid) {
        setTokenValidation({ valid: true, error: null })
        setUserInfo(result.user)
        setStep('form')
        console.log('✅ Activation token validated successfully')
      } else {
        setTokenValidation({ valid: false, error: result.error || 'Invalid activation token' })
        setStep('error')
        console.error('❌ Token validation failed:', result.error)
      }
    } catch (error) {
      console.error('❌ Error validating token:', error)
      setTokenValidation({ valid: false, error: 'Failed to validate activation token' })
      setStep('error')
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')

    // Validate password requirements
    const validation = validatePassword(formData.password, formData.confirmPassword)

    if (!validation.hasMinLength) {
      setError('Password must be at least 8 characters long')
      return
    }

    if (!validation.hasLowercase) {
      setError('Password must contain at least one lowercase letter (a-z)')
      return
    }

    if (!validation.hasUppercase) {
      setError('Password must contain at least one uppercase letter (A-Z)')
      return
    }

    if (!validation.hasNumber) {
      setError('Password must contain at least one number (0-9)')
      return
    }

    if (!validation.hasSpecialChar) {
      setError('Password must contain at least one special character (!@#$%^&*()_+-=[]{}...)')
      return
    }

    if (!validation.passwordsMatch) {
      setError('Passwords do not match')
      return
    }

    setSubmitting(true)

    try {
      const response = await fetch('/api/auth/activate-account', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: token,
          password: formData.password
        }),
      })

      const result = await response.json()

      if (response.ok && result.success) {
        console.log('✅ Account activated successfully')

        // Store the activation result for the success step
        setActivationResult(result)
        setStep('success')

        // Automatically sign in the user with their new credentials
        try {
          console.log('🔐 Automatically signing in user...')
          await signIn(result.user.email, formData.password)

          // Redirect to the provided redirectUrl after a short delay
          setTimeout(() => {
            if (result.redirectUrl) {
              console.log('🚀 Redirecting to:', result.redirectUrl)
              router.push(result.redirectUrl)
            } else {
              // Fallback to complete profile if no redirectUrl provided
              router.push('/admin/complete-profile')
            }
          }, 3000) // 3 second delay to show success message

        } catch (signInError) {
          console.error('❌ Auto sign-in failed:', signInError)
          // If auto sign-in fails, still show success but redirect to login
          setTimeout(() => {
            router.push('/staff-login')
          }, 3000)
        }

      } else {
        setError(result.error || 'Failed to activate account')
        console.error('❌ Account activation failed:', result.error)
      }
    } catch (error) {
      console.error('❌ Error activating account:', error)
      setError('An error occurred while activating your account')
    } finally {
      setSubmitting(false)
    }
  }

  const validatePassword = (password, confirmPassword) => {
    const validation = {
      hasLowercase: /[a-z]/.test(password),
      hasUppercase: /[A-Z]/.test(password),
      hasNumber: /[0-9]/.test(password),
      hasSpecialChar: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?`~]/.test(password),
      hasMinLength: password.length >= 8,
      passwordsMatch: password === confirmPassword && password.length > 0
    }
    setPasswordValidation(validation)
    return validation
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target
    const newFormData = {
      ...formData,
      [name]: value
    }
    setFormData(newFormData)

    // Real-time password validation
    if (name === 'password' || name === 'confirmPassword') {
      validatePassword(
        name === 'password' ? value : newFormData.password,
        name === 'confirmPassword' ? value : newFormData.confirmPassword
      )
    }
  }

  // Show loading state while validating token
  if (step === 'validating') {
    return (
      <>
        <Head>
          <title>Activating Account - Ocean Soul Sparkles</title>
        </Head>
        <div className={styles.loginContainer}>
          <div className={styles.loginCard}>
            <div className={styles.logoContainer}>
              <img
                src="/images/bannerlogo.PNG"
                alt="Ocean Soul Sparkles Logo"
                className={styles.logo}
              />
            </div>
            <h1 className={styles.title}>Activating Your Account</h1>
            <div className={styles.loading}>
              <p>Validating your activation link...</p>
              <div className={styles.spinner}></div>
            </div>
          </div>
        </div>
      </>
    )
  }

  // Show error state for invalid tokens
  if (step === 'error') {
    return (
      <>
        <Head>
          <title>Activation Error - Ocean Soul Sparkles</title>
        </Head>
        <div className={styles.loginContainer}>
          <div className={styles.loginCard}>
            <div className={styles.logoContainer}>
              <img
                src="/images/bannerlogo.PNG"
                alt="Ocean Soul Sparkles Logo"
                className={styles.logo}
              />
            </div>
            <h1 className={styles.title}>Activation Error</h1>
            <div className={styles.error}>
              <p>{tokenValidation.error || 'Invalid or expired activation link'}</p>
              <p>Please contact your administrator for a new activation link.</p>
            </div>
            <button
              onClick={() => window.location.href = 'https://www.oceansoulsparkles.com.au'}
              className={styles.submitButton}
            >
              Return to Home
            </button>
          </div>
        </div>
      </>
    )
  }

  // Show success state after activation
  if (step === 'success') {
    return (
      <>
        <Head>
          <title>Account Activated - Ocean Soul Sparkles</title>
        </Head>
        <div className={styles.loginContainer}>
          <div className={styles.loginCard}>
            <div className={styles.logoContainer}>
              <img
                src="/images/bannerlogo.PNG"
                alt="Ocean Soul Sparkles Logo"
                className={styles.logo}
              />
            </div>
            <h1 className={styles.title}>🎉 Account Activated!</h1>
            <div className={styles.success}>
              <div className={styles.successIcon}>🎉</div>
              <h2>Welcome to the Team!</h2>
              <p>Congratulations! Your Ocean Soul Sparkles account has been successfully activated.</p>
              <p>You are being automatically signed in and redirected to complete your profile...</p>

              <div className={styles.nextStepsBox}>
                <h3>What's Next?</h3>
                <ul>
                  <li>Complete your profile with additional details</li>
                  <li>Access your staff portal to view your dashboard</li>
                  <li>Review your schedule and upcoming bookings</li>
                  <li>Connect with other team members</li>
                </ul>
              </div>

              <div className={styles.loading}>
                <p>Redirecting you to your profile setup...</p>
                <div className={styles.spinner}></div>
              </div>
            </div>

            <div className={styles.buttonGroup}>
              <button
                onClick={() => {
                  if (activationResult?.redirectUrl) {
                    router.push(activationResult.redirectUrl)
                  } else {
                    router.push('/admin/complete-profile')
                  }
                }}
                className={`${styles.submitButton} ${styles.primaryButton}`}
              >
                🚀 Continue to Profile Setup
              </button>
              <button
                onClick={() => window.location.href = 'https://www.oceansoulsparkles.com.au'}
                className={`${styles.submitButton} ${styles.secondaryButton}`}
              >
                🏠 Return to Home
              </button>
            </div>
          </div>
        </div>
      </>
    )
  }

  // Show password creation form
  return (
    <>
      <Head>
        <title>Create Your Password - Ocean Soul Sparkles</title>
      </Head>
      <div className={styles.loginContainer}>
        <div className={styles.loginCard}>
          <div className={styles.logoContainer}>
            <img
              src="/images/bannerlogo.PNG"
              alt="Ocean Soul Sparkles Logo"
              className={styles.logo}
            />
          </div>
          
          <h1 className={styles.title}>Welcome to Ocean Soul Sparkles!</h1>
          
          {userInfo && (
            <div className={styles.welcomeMessage}>
              <p>Hello <strong>{userInfo.name}</strong>!</p>
              <p>Your {userInfo.role === 'artist' ? 'Beauty Artist' : 'Hair Braiding Specialist'} application has been approved.</p>
              <p>Please create a secure password to activate your account:</p>
            </div>
          )}

          <div className={styles.passwordRequirements}>
            <h3>Password Requirements</h3>
            <p>Your password must meet all of the following criteria:</p>
            <div className={styles.requirementsList}>
              <div className={`${styles.requirement} ${passwordValidation.hasMinLength ? styles.valid : styles.invalid}`}>
                <span className={styles.icon}>{passwordValidation.hasMinLength ? '✅' : '❌'}</span>
                At least 8 characters long
              </div>
              <div className={`${styles.requirement} ${passwordValidation.hasLowercase ? styles.valid : styles.invalid}`}>
                <span className={styles.icon}>{passwordValidation.hasLowercase ? '✅' : '❌'}</span>
                One lowercase letter (a-z)
              </div>
              <div className={`${styles.requirement} ${passwordValidation.hasUppercase ? styles.valid : styles.invalid}`}>
                <span className={styles.icon}>{passwordValidation.hasUppercase ? '✅' : '❌'}</span>
                One uppercase letter (A-Z)
              </div>
              <div className={`${styles.requirement} ${passwordValidation.hasNumber ? styles.valid : styles.invalid}`}>
                <span className={styles.icon}>{passwordValidation.hasNumber ? '✅' : '❌'}</span>
                One number (0-9)
              </div>
              <div className={`${styles.requirement} ${passwordValidation.hasSpecialChar ? styles.valid : styles.invalid}`}>
                <span className={styles.icon}>{passwordValidation.hasSpecialChar ? '✅' : '❌'}</span>
                One special character (!@#$%^&*...)
              </div>
              <div className={`${styles.requirement} ${passwordValidation.passwordsMatch ? styles.valid : styles.invalid}`}>
                <span className={styles.icon}>{passwordValidation.passwordsMatch ? '✅' : '❌'}</span>
                Passwords match
              </div>
            </div>
          </div>

          <form onSubmit={handleSubmit} className={styles.form}>
            <div className={styles.inputGroup}>
              <label htmlFor="password">Create Password</label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                required
                minLength={8}
                placeholder="Enter a secure password"
                className={styles.input}
              />
            </div>

            <div className={styles.inputGroup}>
              <label htmlFor="confirmPassword">Confirm Password</label>
              <input
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                required
                placeholder="Confirm your password"
                className={styles.input}
              />
            </div>

            {error && (
              <div className={styles.error}>{error}</div>
            )}

            <button
              type="submit"
              disabled={submitting}
              className={styles.submitButton}
            >
              {submitting ? 'Activating Account...' : 'Activate My Account'}
            </button>
          </form>

          <div className={styles.helpText}>
            <p>Need help? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
          </div>
        </div>
      </div>
    </>
  )
}
